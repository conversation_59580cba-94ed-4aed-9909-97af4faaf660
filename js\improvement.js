﻿// 页面加载时初始化聊天
// document.addEventListener('DOMContentLoaded', function() {
//     addMessageToHistory('ai', '我是字节跳动AI大模型，很高兴见到你！ 我可以帮你做很多事情，请把你的任务交给我吧~');
// });

// 发送消息
async function sendMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();
    if (!message) return;

    // 添加用户消息到聊天历史
    addMessageToHistory('user', message);
    addMessageToHistory('ai', '思考中…');
    input.value = '';

    try {
        // 通过后端代理调用 AI API
        const response = await fetch('php/chat.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message
            })
        });

        const data = await response.json();
        if (data.success && data.response) {
            addMessageToHistory('ai', data.response);
        } else {
            throw new Error(data.message || 'Invalid response from server');
        }
    } catch (error) {
        console.error('Error:', error);
        addMessageToHistory('ai', '抱歉，处理您的请求时出现错误。');
    }
}

// 添加消息到聊天历史
async function addMessageToHistory(role, content) {
    const chatHistory = document.getElementById('chatHistory');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}-message`;

    // 将内容按###分割成数组 
    const contentParts = content.split(/###|- \*\*/); 
    // 逐个处理分割后的部分并添加到messageDiv中 
    for (let i = 0; i < contentParts.length; i++) { 
        const part = contentParts[i]; 
        if (i > 0) { 
            // 如果不是第一个部分，就添加两个换行元素 
            const br1Element = document.createElement('br'); 
            const br2Element = document.createElement('br'); 
            messageDiv.appendChild(br1Element); 
            messageDiv.appendChild(br2Element); 
        } 
            const textNode = document.createTextNode(part); 
        messageDiv.appendChild(textNode); 
    } 
    chatHistory.appendChild(messageDiv); 
    chatHistory.scrollTop = chatHistory.scrollHeight;

    // 记录日志
    if (role === 'user' || (role === 'ai' && content !== '思考中…')) {
        try {
            const logData = {
                message: role === 'user' ? content : '',
                response: role === 'ai' ? content : ''
            };
            //console.log('Sending log data:', logData);
            
            const response = await fetch('php/log_chat.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(logData)
            });
            
            const result = await response.json();
            //console.log('Log response:', result);
            
            if (!result.success) {
                console.error('Failed to log chat:', result.message);
            }
        } catch (error) {
            console.error('Error logging chat:', error);
        }
    }
} 