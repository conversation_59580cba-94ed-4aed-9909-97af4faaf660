<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css">
    <link href="css/layui.css" rel="stylesheet" />
    <script src="js/tabs.js" defer></script>
    <script src="js/index.js" defer></script>
    <script src="js/components.js" defer></script>
    <style>
        /* 确保html和body占满全屏 */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden; /* 防止外层滚动条 */
        }

        /* 设置layui-layout-body占满全屏 */
        .layui-layout-body {
            height: 100vh;
            overflow: hidden;
        }

        /* 设置layui-layout占满全屏 */
        .layui-layout.layui-layout-admin {
            height: 100vh;
        }

        /* 设置layui-body的高度和滚动 */
        .layui-body {
            height: calc(100vh - 60px); /* 减去header高度 */
            overflow: hidden; /* 取消外层滚动条 */
        }

        /* 设置tab容器高度 */
        .layui-tab {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* 设置tab内容区域 */
        .layui-tab-content {
            flex: 1;
            overflow: hidden;
        }

        /* 设置tab项高度 */
        .layui-tab-item {
            height: 100%;
            overflow: hidden;
        }

        /* iframe样式调整 */
        iframe {
            width: 100%;
            height: 100%; /* 改为100%以充满容器 */
            border: none;
            display: block;
        }

        /* 首页内容区域滚动设置 */
        .main-content {
            height: 100%;
            overflow-y: auto; /* 只有首页内容可以滚动 */
            padding: 20px;
            box-sizing: border-box;
        }
    </style>
</head>

<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div class="layui-header" lay-allowClose="true">
            <div class="header-left">
                <img src="./pic/01_TCL 华星_Logo_RGB_标准.png" width="120px" height="25px">
                <span class="header-title">设备综合管理界面</span>
            </div>
            <div class="header-right">
                <span class="header-icon">✉</span>
                <div class="user-profile">
                    <div class="user-avatar" id="avatarText"></div>
                    <span id="headerUsername"></span>
                </div>
            </div>
        </div>

        <div class="layui-side">
            <div class="layui-side-scroll">
                <!-- 左侧导航区域（可配合layui已有的垂直导航） -->
                <ul class="layui-nav layui-nav-tree" lay-filter="test" style="margin-top: 30px">
                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="index.html"><span class="menu-icon">🏠</span>首&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;页</a>
                    </li>
                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">📋</span>交接管理</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="associate.html?tab=search" data-id="associate-search" data-title="交接查询" class="site-demo-active" data-type="tabAdd">交接查询</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="associate.html?tab=register" data-id="associate-register" data-title="交接登录" class="site-demo-active" data-type="tabAdd">交接登录</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="associate.html?tab=analysis" data-id="associate-analysis" data-title="交接分析" class="site-demo-active" data-type="tabAdd">交接分析</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">🧰</span>大故障</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="fault.html?tab=search" data-id="fault-search" data-title="故障查询" class="site-demo-active" data-type="tabAdd">故障查询</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="fault.html?tab=register" data-id="fault-register" data-title="故障登录" class="site-demo-active" data-type="tabAdd">故障登录</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">📡</span>现场管理</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="site.html" data-id="site" data-title="现场管理" class="site-demo-active" data-type="tabAdd">现场管理</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">🔨</span>备品管理</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="spareparts.html?tab=splist" data-id="spareparts-list" data-title="备品列表" class="site-demo-active" data-type="tabAdd">备品列表</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="spareparts.html?tab=spneed" data-id="spareparts-need" data-title="备品请购" class="site-demo-active" data-type="tabAdd">备品请购</a>
                            </dd>
                            <dd class="spareparts-admin-only" style="display: none;">
                                <a href="#" data-url="spareparts.html?tab=spload" data-id="spareparts-load" data-title="备品录入" class="site-demo-active" data-type="tabAdd">备品录入</a>
                            </dd>
                            <dd class="spareparts-admin-only" style="display: none;">
                                <a href="#" data-url="spareparts.html?tab=sphistory" data-id="spareparts-history" data-title="备品履历" class="site-demo-active" data-type="tabAdd">备品履历</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">📚</span>部件BOM</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="partsBom.html?tab=bomSearch" data-id="partsBom-search" data-title="BOM查询" class="site-demo-active" data-type="tabAdd">BOM查询</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="partsBom.html?tab=bomRegister" data-id="partsBom-register" data-title="BOM登录" class="site-demo-active" data-type="tabAdd">BOM登录</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">📊</span>切机管理</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="ModelChange.html?tab=JIG" data-id="modelchange-jig" data-title="金型信息" class="site-demo-active" data-type="tabAdd">金型信息</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="ModelChange.html?tab=MC" data-id="modelchange-mc" data-title="切机管理" class="site-demo-active" data-type="tabAdd">切机管理</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">🔧</span>PM管理</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="pm.html?tab=pmSearch" data-id="pm-search" data-title="PM查询" class="site-demo-active" data-type="tabAdd">PM查询</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="pm.html?tab=pmRegister" data-id="pm-register" data-title="PM登录" class="site-demo-active" data-type="tabAdd">PM登录</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">🔋</span>电池管理</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="AGV.html?tab=changelist" data-id="agv-changelist" data-title="电池更换履历" class="site-demo-active" data-type="tabAdd">电池更换履历</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="AGV.html?tab=changein" data-id="agv-changein" data-title="电池更换登录" class="site-demo-active" data-type="tabAdd">电池更换登录</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="AGV.html?tab=battery" data-id="agv-battery" data-title="AGV电池信息" class="site-demo-active" data-type="tabAdd">AGV电池信息</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">📑</span>资料手册</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="teaching.html?tab=teaching" data-id="teaching-manual" data-title="资料手册" class="site-demo-active" data-type="tabAdd">资料手册</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="teaching.html?tab=teachingupload" data-id="teaching-upload" data-title="资料上传" class="site-demo-active" data-type="tabAdd">资料上传</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">📢</span>通知发布</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="notice.html?tab=publish" data-id="notice-publish" data-title="发布通知" class="site-demo-active" data-type="tabAdd">发布通知</a>
                            </dd>
                            <dd>
                                <a href="#" data-url="notice.html?tab=history" data-id="notice-history" data-title="历史记录" class="site-demo-active" data-type="tabAdd">历史记录</a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <a class="layui-nav-tt" href="javascript:;"><span class="menu-icon">🤖</span>AI小助理</a>
                        <dl class="layui-nav-child">
                            <dd>
                                <a href="#" data-url="chat.html" data-id="chat" data-title="AI小助理" class="site-demo-active" data-type="tabAdd">AI小助理</a>
                            </dd>
                        </dl>
                    </li>
                </ul>
            </div>
        </div>
        <div class="layui-body">
            <!-- Tab标签页容器 -->
            <div class="layui-tab" lay-filter="demo" lay-allowclose="true">
                <ul class="layui-tab-title">
                    <li class="layui-this" lay-id="home">首页</li>
                </ul>
                <ul class="rightmenu" style="display: none;position: absolute;">
                    <li data-type="closethis">关闭当前</li>
                    <li data-type="closeall">关闭所有</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 首页内容 -->
                    <div class="layui-tab-item layui-show">
                        <div class="main-content">
                            <!-- 默认显示的主页内容 -->
                            <div id="homeContent" class="page-content active">
              <hr>
              <h3>重要通知</h3>
              <div class="notice-list">
                  <table class="data-table">
                      <thead>
                          <tr>
                              <th>关联事项</th>
                              <th>通知内容</th>
                              <th>附件</th>
                              <th>发布人</th>
                              <th>状态</th>
                              <th>发布时间</th>
                              <th>结束时间</th>
                          </tr>
                      </thead>
                      <tbody id="activeNotices">
                          <!-- 数据将通过 JavaScript 动态加载 -->
                      </tbody>
                  </table>
              </div>
                <hr>

                <h3>待解决问题</h3>
                <div class="associate-list">
                  <table class="data-table">
                      <thead>
                          <tr>
                              <th>LINE</th>
                              <th>UNIT</th>
                              <th>现象</th>
                              <th>原因</th>
                              <th>处理内容</th>
                              <th>故障分类</th>
                              <th>故障部件</th>
                              <th>故障代码</th>
                              <th>状态</th>
                              <th>发布时间</th>
                          </tr>
                      </thead>
                      <tbody id="activeAssociate">
                          <!-- 数据将通过 JavaScript 动态加载 -->
                      </tbody>
                  </table>
                </div>
                <hr>

                <h3>问题处理结果反馈</h3>
                <div class="associate-list-closed">
                  <table class="data-table">
                      <thead>
                          <tr>
                              <th>LINE</th>
                              <th>UNIT</th>
                              <th>现象</th>
                              <th>原因</th>
                              <th>处理内容</th>
                              <th>故障分类</th>
                              <th>故障部件</th>
                              <th>故障代码</th>
                              <th>状态</th>
                              <th>结案时间</th>
                          </tr>
                      </thead>
                      <tbody id="closedAssociate">
                          <!-- 数据将通过 JavaScript 动态加载 -->
                      </tbody>
                  </table>
                </div>
                <hr>

                <h3>切机计划</h3>
                <div class="modelchange-list">
                  <table class="data-table">
                    <tr>
                      <td><h4>待添加</h4>
                      <br><br><br><br></td>
                    </tr>
                  </table>
              </div>
                <hr>

                <h3>切机履历</h3>
                <table class="data-table">
                  <tr>
                    <td><h4>待添加</h4>
                    <br><br><br><br></td>
                  </tr>
                </table>
                <hr>

                <h3>备品追加进度</h3>
                <table class="data-table">
                  <tr>
                    <td><h4>待添加</h4>
                    <br><br><br><br></td>
                  </tr>
                </table>
                <hr>
                            </div>
                        </div>
                    </div>
                    <!-- 其他tab内容将通过JavaScript动态添加 -->
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="js/jquery-1.11.3.min.js"></script>
    <script src="js/layui.js"></script>

    <script type="text/javascript">
        //添加编辑弹出层
        function updatePwd(title, id) {
            $.jq_Panel({
                title: title,
                iframeWidth: 500,
                iframeHeight: 300,
                url: "updatePwd.html"
            });
        }
    </script>


    <script>
        $("body").click(function () { $('.rightmenu').hide(); });
        //JavaScript代码区域
        // 权限控制和用户信息初始化
        document.addEventListener('DOMContentLoaded', function() {
            const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

            // 显示用户信息
            if (userInfo.username || userInfo.name) {
                const username = userInfo.username || userInfo.name;
                document.getElementById('headerUsername').textContent = username;
                document.getElementById('avatarText').textContent = username.charAt(0).toUpperCase();

                // 添加用户名点击事件（显示下拉菜单）
                const headerUsername = document.getElementById('headerUsername');
                headerUsername.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡

                    const menu = document.createElement('div');
                    menu.className = 'user-menu';
                    menu.innerHTML = `
                        <div class="menu-item" onclick="showUserInfo()">个人信息</div>
                        <div class="menu-item" onclick="logout()">退出登录</div>
                    `;

                    // 移除任何现有的菜单
                    const existingMenu = document.querySelector('.user-menu');
                    if (existingMenu) {
                        existingMenu.remove();
                    }

                    // 将菜单添加到用户名元素后
                    headerUsername.appendChild(menu);

                    // 点击其他地方关闭菜单
                    document.addEventListener('click', function closeMenu(e) {
                        if (!headerUsername.contains(e.target)) {
                            menu.remove();
                            document.removeEventListener('click', closeMenu);
                        }
                    });
                });
            }

            // 权限控制 - 显示备品管理的管理员选项
            if (userInfo.splevel === 1) {
                document.querySelectorAll('.spareparts-admin-only').forEach(item => {
                    item.style.display = 'block';
                });
            }
        });

        layui.use('element', function () {
            var $ = layui.jquery;
            var element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块

            //触发事件
            var active = {
                //在这里给active绑定几项事件，后面可通过active调用这些事件
                tabAdd: function (url, id, name) {
                    //新增一个Tab项 传入三个参数，分别对应其标题，tab页面的地址，还有一个规定的id，是标签中data-id的属性值
                    //关于tabAdd的方法所传入的参数可看layui的开发文档中基础方法部分
                    element.tabAdd('demo', {
                        title: name,
                        content: '<iframe data-frameid="' + id + '" scrolling="auto" frameborder="0" src="' + url +'" style="width:100%;height:99%;"></iframe>',
                        id: id //规定好的id
                    })
                    CustomRightClick(id); //给tab绑定右击事件
                    FrameWH();  //计算ifram层的大小
                },
                tabChange: function (id) {
                    //切换到指定Tab项
                    element.tabChange('demo', id); //根据传入的id传入到指定的tab项
                },
                tabDelete: function (id) {
                    element.tabDelete("demo", id);//删除
                }
                , tabDeleteAll: function (ids) {//删除所有
                    $.each(ids, function (i, item) {
                        element.tabDelete("demo", item); //ids是一个数组，里面存放了多个id，调用tabDelete方法分别删除
                    })
                }
            };


            //当点击有site-demo-active属性的标签时，即左侧菜单栏中内容 ，触发点击事件
            $('.site-demo-active').on('click', function () {
                var dataid = $(this);

                //这时会判断右侧.layui-tab-title属性下的有lay-id属性的li的数目，即已经打开的tab项数目
                if ($(".layui-tab-title li[lay-id]").length <= 0) {
                                    //如果比零小，则直接打开新的tab项
                    active.tabAdd(dataid.attr("data-url"), dataid.attr("data-id"), dataid.attr("data-title"));
                } else {
                    //否则判断该tab项是否以及存在

                    var isData = false; //初始化一个标志，为false说明未打开该tab项 为true则说明已有
                    $.each($(".layui-tab-title li[lay-id]"), function () {
                        //如果点击左侧菜单栏所传入的id 在右侧tab项中的lay-id属性可以找到，则说明该tab项已经打开
                        if ($(this).attr("lay-id") == dataid.attr("data-id")) {
                            isData = true;
                        }
                    })
                    if (isData == false) {
                        //标志为false 新增一个tab项
                        active.tabAdd(dataid.attr("data-url"), dataid.attr("data-id"), dataid.attr("data-title"));
                    }
                }
                //最后不管是否新增tab，最后都转到要打开的选项页面上

                active.tabChange(dataid.attr("data-id"));
            });

            function CustomRightClick(id) {
                //取消右键  rightmenu属性开始是隐藏的 ，当右击的时候显示，左击的时候隐藏
                $('.layui-tab-title li').on('contextmenu', function () { return false; })
                $('.layui-tab-title,.layui-tab-title li').click(function () {
                    $('.rightmenu').hide();
                });
                //桌面点击右击
                $('.layui-tab-title li').on('contextmenu', function (e) {
                    var popupmenu = $(".rightmenu");
                    popupmenu.find("li").attr("data-id", id); //在右键菜单中的标签绑定id属性

                    //判断右侧菜单的位置
                    l = ($(document).width() - e.clientX) < popupmenu.width() ? (e.clientX - popupmenu.width()) : e.clientX;
                    t = ($(document).height() - e.clientY) < popupmenu.height() ? (e.clientY - popupmenu.height()) : e.clientY;
                    popupmenu.css({ left: l, top: t }).show(); //进行绝对定位
                    //alert("右键菜单")
                    return false;
                });
            }

            $(".rightmenu li").click(function () {

                //右键菜单中的选项被点击之后，判断type的类型，决定关闭所有还是关闭当前。
                if ($(this).attr("data-type") == "closethis") {
                    //如果关闭当前，即根据显示右键菜单时所绑定的id，执行tabDelete
                    active.tabDelete($(this).attr("data-id"))
                } else if ($(this).attr("data-type") == "closeall") {
                    var tabtitle = $(".layui-tab-title li");
                    var ids = new Array();
                    $.each(tabtitle, function (i) {
                        ids[i] = $(this).attr("lay-id");
                    })
                    //如果关闭所有 ，即将所有的lay-id放进数组，执行tabDeleteAll
                    active.tabDeleteAll(ids);
                }

                $('.rightmenu').hide(); //最后再隐藏右键菜单
            })
            function FrameWH() {
                // iframe现在通过CSS自动填充容器，不需要手动计算高度
                // 保留此函数以防其他地方调用，但不执行任何操作
            }

            $(window).resize(function () {

                FrameWH();

            })
        });

        // 显示用户信息弹窗
        function showUserInfo() {
            const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

            // 创建弹窗
            const modal = document.createElement('div');
            modal.className = 'user-info-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>个人信息</h2>
                        <span class="close" onclick="closeUserInfo()">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="user-info">
                            <div class="info-group">
                                <label>姓名：</label>
                                <span>${userInfo.name || userInfo.username || '-'}</span>
                            </div>
                            <div class="info-group">
                                <label>工号：</label>
                                <span>${userInfo.account || '-'}</span>
                            </div>
                            <div class="info-group">
                                <label>部门：</label>
                                <span>${userInfo.department || '-'}</span>
                            </div>
                            <div class="info-group">
                                <label>科室：</label>
                                <span>${userInfo.section || '-'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 点击背景关闭弹窗
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeUserInfo();
                }
            });
        }

        // 关闭用户信息弹窗
        function closeUserInfo() {
            const modal = document.querySelector('.user-info-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 登出函数
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('userInfo');
                window.location.href = 'login.html';
            }
        }
    </script>
</body>

</html>