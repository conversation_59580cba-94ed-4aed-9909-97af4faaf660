﻿body {
    font-family: <PERSON><PERSON>, "Microsoft YaHei", sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.sidebar {
    width: 160px;
    height: 100vh;
    background-color: #f8f9fa;
    position: fixed;
    left: 0;
    top: 60px;
    padding-top: 20px;
    border-right: 1px solid #eee;
    overflow-y: auto;
    z-index: 100;
}

.user-info {
    display: flex;
    /* align-items: center; */
    padding: 0 20px;
    margin-bottom: 20px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: #e9ecef;
    border-radius: 50%;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #666;
}

.user-details {
    color: #333;
}

.user-name {
    font-weight: bold;
    margin: 0;
}

.user-role {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    color: #333;
    text-decoration: none;
    margin: 5px 0;
}

.menu-item:hover {
    background-color: #e9ecef;
}

.menu-icon {
    width: 20px;
    margin-right: 10px;
    text-align: center;
    color: #666;
}

.main-content {
    /* 在新的布局中，main-content是首页tab的内容容器 */
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0; /* 移除margin，因为现在在tab容器内 */
}



h1 {
    font-size: 24px;
    color: #333;
}

h4 {
    color: #666;
    margin-bottom: 10px;
}

hr {
    border: none;
    border-top: 1px solid #eee;
    margin: 20px 0;
}

.poetry {
    font-family: "KaiTi", "楷体", serif;
    line-height: 2;
    margin: 20px 0;
}

.footer {
    margin-top: 20px;
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

em {
    font-style: normal;
    color: #666;
}

strong {
    font-weight: bold;
    color: #333;
}

.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    background: linear-gradient(45deg, #4c6fff, #3a5ae8);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.header-title:hover {
    transform: scale(1.02);
    color: #0056b3;
}

.menu-toggle {
    font-size: 20px;
    color: #666;
    cursor: pointer;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 200px;
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 30px;
}

.header-icon {
    font-size: 20px;
    color: #666;
    cursor: pointer;
    transition: color 0.3s ease;
}

.header-icon:hover {
    color: #4c6fff;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.header-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fault-search {
    background-color: #f5f5f5;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.search-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.search-row label {
    margin-right: 5px;
    white-space: nowrap;
}

.search-row select,
.search-row input {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 120px;
}

.search-btn,
.reset-btn {
    padding: 6px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.search-btn {
    background-color: #4c6fff;
    color: white;
}

.reset-btn {
    background-color: #6c757d;
    color: white;
}

.search-btn:hover {
    background-color: #3a5ae8;
}

.reset-btn:hover {
    background-color: #5a6268;
}

.button-group {
    display: flex;
    gap: 10px;
    margin-left: auto; 
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background-color: #f5f5f5;
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    white-space: nowrap;
}

.data-table td {
    padding: 8px;
    border: 1px solid #ddd;
    vertical-align: middle;
}

.btn-view {
    padding: 4px 8px;
    background-color: #4c6fff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-view:hover {
    background-color: #3a5ae8;
}

.splist-list .btn-delete,
.spneedlist-list .btn-delete {
    margin-top: 4px;
    margin-left: 4px;
    padding: 4px 8px;
    background-color: #eb5151;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.splist-list .btn-delete:hover,
.spneedlist-list .btn-delete:hover {
    background-color: #ec1010;
}

.menu-item.active {
    background-color: #e0e0e0;
}

.tabs {
    margin: 20px 0;
    border-bottom: 2px solid #ddd;
}

.line {
    margin: 20px 0;
    border-bottom: 2px solid #ddd;
}

.tab-button {
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    background: none;
    cursor: pointer;
    margin-right: 10px;
    position: relative;
}

.tab-button.active {
    color: #4c6fff;
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4c6fff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.register-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.form-group textarea {
    resize: vertical;
}

.save-btn,
.submit-btn {
    background-color: #4c6fff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.save-btn:hover,
.submit-btn:hover {
    background-color: #3a5ae8;
}

.page-content {
    display: none;
    background: white;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.page-content.active {
    display: block;
}

/* 切机管理样式 */
.machine-plan, .machine-history {
    padding: 20px;
}

.plan-tools {
    margin-bottom: 20px;
}

.plan-tools button, .export-btn {
    padding: 8px 16px;
    margin-right: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background-color: #4c6fff;
    color: white;
}

.plan-tools button:hover, .export-btn:hover {
    background-color: #3a5ae8;
}

.search-form {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    width: 100%;
    width: 100%;
    box-sizing: border-box;
.btn-delete:hover {
    background-color: #c82333;
}
}

/* 现场管理样式 */
.spotfire-content, .cim-content {
    padding: 20px;
}

.data-visualization {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin: 20px 0;
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-image {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: contain;
}

.cim-dashboard {
    margin-top: 20px;
}

.status-overview {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.status-number {
    font-size: 32px;
    font-weight: bold;
    color: #4c6fff;
}

.status-card.warning .status-number {
    color: #ffc107;
}

.status-card.error .status-number {
    color: #dc3545;
}

.monitor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.monitor-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    background: #f8f9fa;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-indicator.running {
    background: #28a745;
}

.card-body {
    padding: 15px;
}

.parameter {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.param-name {
    color: #666;
}

.param-value {
    font-weight: bold;
}

.btn-view {
    padding: 4px 8px;
    background-color: #4c6fff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-view:hover {
    background-color: #3a5ae8;
}

/* 备品管理样式 */
.bom-content, .parts-request {
    padding: 20px;
}

.request-tools {
    margin-bottom: 20px;
}

.request-tools button {
    padding: 8px 16px;
    margin-right: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background-color: #4c6fff;
    color: white;
}

.request-tools button:hover {
    background-color: #3a5ae8;
}

.request-form {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.purchase-form {
    max-width: 800px;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.cancel-btn {
    padding: 10px 20px;
    border: 1px solid #dc3545;
    border-radius: 4px;
    background: none;
    color: #dc3545;
    cursor: pointer;
}

.cancel-btn:hover {
    background-color: #dc3545;
    color: white;
}

.urgency {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.urgency.normal {
    background-color: #e9ecef;
    color: #495057;
}

.urgency.urgent {
    background-color: #fff3cd;
    color: #856404;
}

.urgency.very-urgent {
    background-color: #f8d7da;
    color: #721c24;
}

.btn-cancel {
    padding: 4px 8px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-cancel:hover {
    background-color: #c82333;
}

/* 数量警告样式 */
.stock-warning {
    color: #dc3545;
    font-weight: bold;
}

/* 表格内的数字右对齐 */
.data-table td:nth-child(5),
.data-table td:nth-child(6),
.data-table td:nth-child(7) {
    text-align: right;
}

/* 设备改善样式 */
.proposal-content, .measure-content {
    padding: 20px;
}

.proposal-tools, .measure-tools {
    margin-bottom: 20px;
}

.proposal-tools button, .measure-tools button {
    padding: 8px 16px;
    margin-right: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background-color: #4c6fff;
    color: white;
}

.proposal-tools button:hover, .measure-tools button:hover {
    background-color: #3a5ae8;
}

.measure-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.measure-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.measure-card .card-header {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.measure-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.measure-status.implementing {
    background-color: #cce5ff;
    color: #004085;
}

.measure-card .card-body {
    padding: 15px;
}

.measure-info {
    margin: 15px 0;
}

.measure-info p {
    margin: 5px 0;
}

.progress-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 15px 0;
}

.progress {
    height: 100%;
    background-color: #4c6fff;
    transition: width 0.3s ease;
}

.measure-steps {
    margin-top: 15px;
}

.measure-steps ul {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.measure-steps li {
    padding: 8px 0;
    padding-left: 24px;
    position: relative;
    color: #666;
}

.measure-steps li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #ddd;
}

.measure-steps li.completed {
    color: #28a745;
}

.measure-steps li.completed::before {
    background-color: #28a745;
    border-color: #28a745;
}

.measure-steps li.active {
    color: #4c6fff;
    font-weight: bold;
}

.measure-steps li.active::before {
    border-color: #4c6fff;
}

.card-footer {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-update, .btn-detail {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-update {
    background-color: #4c6fff;
    color: white;
}

.btn-detail {
    background-color: #6c757d;
    color: white;
}

.btn-update:hover {
    background-color: #3a5ae8;
}

.btn-detail:hover {
    background-color: #5a6268;
}

/* 操作手册样式 */
.manual-content, .training-content {
    padding: 20px;
}

.manual-tools, .training-tools {
    margin-bottom: 20px;
}

.manual-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.manual-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.manual-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
}

.manual-icon.pdf {
    background-color: #dc3545;
}

.manual-icon.word {
    background-color: #0d6efd;
}

.manual-info {
    flex: 1;
}

.manual-info h3 {
    margin: 0 0 5px 0;
    font-size: 16px;
}

.manual-meta {
    color: #666;
    font-size: 12px;
    display: flex;
    gap: 15px;
}

.manual-tags {
    margin-top: 8px;
}

.tag {
    display: inline-block;
    padding: 2px 8px;
    background: #e9ecef;
    border-radius: 12px;
    font-size: 12px;
    color: #495057;
    margin-right: 5px;
}

.manual-actions {
    display: flex;
    gap: 8px;
}

/* 培训资料样式 */
.training-categories {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.category-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: transform 0.2s;
}

.category-card:hover {
    transform: translateY(-2px);
}

.category-card.active {
    border: 2px solid #4c6fff;
}

.category-card h3 {
    margin: 0 0 5px 0;
}

.doc-count {
    color: #666;
    font-size: 14px;
}

.training-list {
    margin-top: 30px;
}

.training-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.training-type {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.training-type.video {
    background-color: #ffc107;
    color: white;
}

.training-type.document {
    background-color: #28a745;
    color: white;
}

.training-details {
    flex: 1;
}

.training-details h3 {
    margin: 0 0 8px 0;
}

.training-desc {
    color: #666;
    margin: 0 0 8px 0;
}

.training-meta {
    color: #666;
    font-size: 14px;
    display: flex;
    gap: 20px;
}

.training-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.progress-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    position: absolute;
    width: 54px;
    height: 54px;
    border-radius: 50%;
    background: white;
}

.progress-text {
    position: relative;
    z-index: 1;
    font-weight: bold;
    color: #4c6fff;
}

.btn-continue, .btn-review {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.btn-continue {
    background-color: #4c6fff;
    color: white;
}

.btn-review {
    background-color: #6c757d;
    color: white;
}

/* 通知发布样式 */
.notice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.notice-tools {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.notice-tools button {
    padding: 8px 16px;
    background-color: #4c6fff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.notice-tools button:hover {
    background-color: #3a5ae8;
}



.notice-form .form-group {
    margin-bottom: 20px;
}

.notice-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}


.notice-form input[type="text"],
.notice-form select,
.notice-form textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}


.notice-form textarea {
    min-height: 200px;
    resize: vertical;
}

.spneed-form input[type="text"] ,
.spneed-form input[type="number"],
.spload-form input[type="text"] ,
.spload-form input[type="number"] {
    font-size: 16px;
    height: 32px ;
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}


/* 响应式调整 */
@media screen and (max-width: 1200px) {
    .search-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-row > * {
        margin-bottom: 10px;
    }
    
    .search-row button {
        width: 100%;
    }
}

/* 表格内容布局优化 */
.data-table td {
    vertical-align: middle;
}



/* 操作按钮列度控制 */
.data-table th:last-child,
.data-table td:last-child {
    width: 50px;
    text-align: center;
}

/* 状态列样式优化 */
.data-table td .status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    text-align: center;
    min-width: 80px;
}

/* 确保其他选项卡内容也正确隐藏和显示 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.notice-tab {
    display: none;
}

.notice-tab.active {
    display: block;
}

/* 选项卡容器样式调整 */
.tabs, .notice-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
}

.tabs-left {
    display: flex;
    gap: 10px;
}

/* 返回首页按钮样式 */
.btn-home {
    padding: 8px 16px;
    background-color: #ffffff;
    color: rgb(0, 0, 0);
    border: 1px solid #1082da;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-home:hover {
    background-color: #1082da;
    color: rgb(255, 255, 255);
}

.btn-home::before {
    content: "🏠";
    font-size: 14px;
}

/* 通知模块样式补充 */
.notice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.notice-tools {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.notice-tools button {
    padding: 8px 16px;
    background-color: #4c6fff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.notice-tools button:hover {
    background-color: #3a5ae8;
}

.save-btn,
.submit-btn {
    background-color: #4c6fff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.save-btn:hover,
.submit-btn:hover {
    background-color: #3a5ae8;
}

/* 编辑器工具栏样式 */
.editor-toolbar {
    background: #f8f9fa;
    padding: 8px;
    border: 1px solid #ddd;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    display: flex;
    gap: 5px;
}

.tool-btn {
    padding: 6px 12px;
    background: none;
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
}

.tool-btn:hover {
    background: #e9ecef;
    border-color: #ddd;
}

.separator {
    color: #ddd;
    margin: 0 5px;
}

.file-upload {
    margin-top: 10px;
}

.upload-area {
    border: 2px dashed #ddd;
    padding: 20px;
    text-align: center;
    border-radius: 4px;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #4c6fff;
}

.file-limit {
    display: block;
    color: #666;
    font-size: 12px;
    margin-top: 5px;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.preview-btn {
    background-color: #6c757d;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.notice-filters {
    margin-bottom: 20px;
}

.notice-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.notice-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notice-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.notice-item.urgent .notice-badge {
    background-color: #dc3545;
    color: white;
}

.notice-item .notice-badge.normal {
    background-color: #28a745;
    color: white;
}

.notice-content {
    flex: 1;
}

.notice-content h3 {
    margin: 0 0 8px 0;
}

.notice-excerpt {
    color: #666;
    margin: 0 0 8px 0;
}

.notice-meta {
    display: flex;
    gap: 20px;
    color: #666;
    font-size: 14px;
}

.attachment-info {
    color: #4c6fff;
}

.notice-actions {
    display: flex;
    gap: 8px;
}

.save-draft {
    background: none;
    border: 1px solid #4c6fff;
    color: #4c6fff;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
}

.save-draft:hover {
    background-color: #4c6fff;
    color: white;
}

/* 修改选项卡样式，使其与其他模块一致 */
.notice-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
}

/* 通知编辑器表格样式 */
.notice-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.notice-textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    min-height: 60px;
}

.notice-datetime {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.btn-add {
    padding: 6px 12px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-add:hover {
    background-color: #218838;
}

/* 调整表格单元格宽度 */
.notice-editor .data-table td:nth-child(1) {
    width: 20%;
}

.notice-editor .data-table td:nth-child(2) {
    width: 50%;
}

.notice-editor .data-table td:nth-child(3) {
    width: 20%;
}

.notice-editor .data-table td:nth-child(4) {
    width: 10%;
    text-align: center;
}

/* 通知选项卡样式 */
.notice-tab {
    display: none;
    padding: 20px 0;
}

.notice-tab.active {
    display: block;
}

/* 选项卡按钮样式 */
.notice-tabs .tab-button {
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    background: none;
    cursor: pointer;
    margin-right: 10px;
    position: relative;
}

.notice-tabs .tab-button.active {
    color: #4c6fff;
}

.notice-tabs .tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4c6fff;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    max-height: 3000px;
    overflow:scroll;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 50px auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    color: #333;
}

.close {
    font-size: 24px;
    color: #666;
    cursor: pointer;
}

.close:hover {
    color: #333;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 14px;
}

.detail-table th,
.detail-table td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: center;
}

.detail-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    white-space: nowrap;
}

.detail-sections {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin: 20px 0;
}

.detail-section,
.change-status {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
}

.detail-section h3,
.change-status h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #333;
}

.section-content {
    color: #666;
    line-height: 1.6;
}

.image-section {
    display: flex;
    gap: 10px;
    overflow-x: auto;
}

.image-section img {
    max-width: 200px;
    height: auto;
    border-radius: 4px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}



.history-title h3{
    text-align:center;
}

.history-body{
    width: 100%;
    height:100%;
    overflow:scroll;
}

.history-body-table {
    width: 100%;
    height: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 14px;
}

.history-body-table th,
.history-body-table td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: center;
}

.history-body-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    white-space: nowrap;
}



.btn-return,
.btn-modify {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-return {
    background-color: #6c757d;
    color: white;
}

.btn-modify {
    background-color: #4c6fff;
    color: white;
}

.btn-return:hover {
    background-color: #5a6268;
}

.btn-modify:hover {
    background-color: #3a5ae8;
}

/* 故障登录表单样式 */
.fault-register {
    background: white;
    padding: 20px;
    border-radius: 4px;
}

.register-form {
    max-width: none;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.full-width {
    width: 100% !important;
}

.upload-area {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.btn-upload,
.btn-delete {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-upload {
    background-color: #4c6fff;
    color: white;
}

.btn-delete {
    background-color: #dc3545;
    color: white;
}

.btn-upload:hover {
    background-color: #3a5ae8;
}

.btn-delete:hover {
    background-color: #c82333;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.btn-submit,
.btn-cancel {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.btn-submit {
    background-color: #4CAF50;
    color: white;
}

.btn-cancel {
    background-color: #6c757d;
    color: white;
}

.btn-submit:hover {
    background-color: #3a5ae8;
}

.btn-cancel:hover {
    background-color: #5a6268;
}

/* 分页控件样式 */
.pagination {
    display: flex;
    justify-content: flex-end; /* 整体右对齐 */
    align-items: center;
    margin-top: 20px;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 4px;
}

.pagination-info,
.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 20px; /* 添加左边距，分隔两个部分 */
}

.page-size {
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 60px;
}

.current-page {
    width: 50px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.pagination button {
    padding: 4px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
}

.pagination button:hover {
    background: #e9e9e9;
}

.pagination button:disabled {
    background: #f5f5f5;
    cursor: not-allowed;
    color: #999;
}

.page-info {
    display: flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap; /* 防止文字换行 */
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;  /* 使标签可以居中 */
    min-width: 60px;       /* 设置最小宽度保持一致性 */
    text-align: center;    /* 标签文字居中 */
}

.status-active {
    background-color: #28a745;
    color: white;
}

.status-expired {
    background-color: #dc3545;
    color: white;
}

.status-running {
    background-color: #3591dc;
    color: white;
}

/* 通知列表表格样式 */
.notice-list .data-table {
    table-layout: fixed;  /* 使用固定列宽布局 */
    width: 100%;
}

.notice-list .data-table th,
.notice-list .data-table td {
    padding: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 设置各列宽度和对齐方式 */
.notice-list .data-table th:nth-child(1), /* 关联事项 */
.notice-list .data-table td:nth-child(1) {
    width: 8%;
    text-align: center;
}

.notice-list .data-table th:nth-child(2)/* 通知内容 */
{
    width: 35%;
    text-align: center;
}
.notice-list .data-table td:nth-child(2) {
    width: 35%;
    text-align: left;  /* 内容左对齐更易读 */
    white-space: normal;  /* 允许文本换行 */
    word-wrap: break-word;  /* 长单词换行 */
    max-height: 100px;  /* 限制最大高度 */
    overflow-y: auto;  /* 内容过多时显示滚动条 */
    padding: 10px;  /* 增加内边距提高可读性 */
}

.notice-list .data-table th:nth-child(3), /* 发布人 */
.notice-list .data-table td:nth-child(3) {
    width: 8%;
    text-align: center;
}

.notice-list .data-table th:nth-child(4), /* 状态 */
.notice-list .data-table td:nth-child(4) {
    width: 8%;
    text-align: center;  
}

.notice-list .data-table th:nth-child(5), /* 发布时间 */
.notice-list .data-table td:nth-child(5) {
    width: 8%;
    text-align: center;  
}

.notice-list .data-table th:nth-child(6), /* 结束时间 */
.notice-list .data-table td:nth-child(6) {
    width: 8%;
    text-align: center;  
}

/* 删除按钮样式 */
.notice-list .btn-delete {
    padding: 4px 8px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.notice-list .btn-delete:hover {
    background-color: #c82333;
}

/* 修改表格列宽 */
.notice-list .data-table th:nth-child(7), /* 操作列 */
.notice-list .data-table td:nth-child(7) {
    width: 5%;
    text-align: center;
}

/* 交接登录表格样式 */
.splist-table ,
.register-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.splist-table th,
.splist-table td,
.register-table th,
.register-table td {
    padding: 10px;
    border: 1px solid #ddd;
}

.splist-table th,
.register-table th {
    background-color: #f5f5f5;
    text-align: right;
    width: 100px;
    white-space: nowrap;
}

.splist-table td,
.register-table td {
    background-color: #fff;
}

.splist-table select,
.splist-table input[type="text"],
.splist-table textarea,
.register-table select,
.register-table input[type="text"],
.register-table textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.splist-table textarea,
.register-table textarea {
    resize: vertical;
    min-height: 60px;
}

/* 文件上传区域样式 */
.upload-area {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.btn-upload {
    padding: 8px 15px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-upload:hover {
    background-color: #45a049;
}

.file-list {
    margin-top: 10px;
}

.file-item {
    display: flex;
    align-items: center;
    margin: 5px 0;
    padding: 5px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.file-name {
    flex: 1;
    margin-right: 10px;
}

.file-size {
    color: #666;
    margin-right: 10px;
}

.btn-delete-file {
    background: none;
    border: none;
    color: #ff4444;
    cursor: pointer;
    font-size: 18px;
    padding: 0 5px;
}

/* 表单操作按钮样式 */
.form-actions {
    text-align: center;
    margin-top: 20px;
}

.form-actions button {
    padding: 10px 20px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-submit {
    background-color: #4c6fff;
    color: white;
}

.btn-reset {
    background-color: #6c757d;
    color: white;
}

.btn-submit:hover {
    background-color: #3a5ae8;
}

.btn-reset:hover {
    background-color: #5a6268;
}

/* 修改register-table的样式 */
.splist-table,
.register-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.splist-table th,
.splist-table td,
.register-table th,
.register-table td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: center;
    vertical-align: middle;
}

.splist-table th,
.register-table th {
    background-color: #f5f5f5;
    white-space: nowrap;
    font-weight: bold;
}

.splist-table select,
.splist-table input[type="text"],
.splist-table textarea,
.register-table select,
.register-table input[type="text"],
.register-table textarea {
    width: 100%;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.splist-table textarea,
.register-table textarea {
    resize: vertical;
    min-height: 40px;
}

/* 添加行按钮样式 */
.btn-add-rows {
    background-color: #4c6fff;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.btn-add-rows:hover {
    background-color: #3a5ae8;
}

/* 单选按钮组样式 */
.radio-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    padding: 8px;
    height: 100%;
    box-sizing: border-box;
}

.radio-group label {
    display: inline-flex;  /* 改为inline-flex */
    align-items: center;
    gap: 5px;
    cursor: pointer;
    margin: 0 5px;  /* 添加水平间距 */
}

.radio-group input[type="radio"] {
    margin: 0;
    cursor: pointer;
    width: auto;  /* 防止继承100%宽度 */
}

.radio-group span {
    font-size: 14px;
    white-space: nowrap;  /* 防止文字换行 */
}

/* 确保单选按钮单元格与其他单元格对齐 */
.splist-table td.radio-group,
.register-table td.radio-group {
    vertical-align: middle;
    padding: 8px;
    height: 100%;
    min-height: 40px;  /* 与其他单元格保持一致的最小高度 */
}

/* 修改删除按钮样式 */
.action-column {
    width: 60px;
    padding: 8px !important;
}

.btn-delete-row {
    background-color: #ce3746;
    color: #ffffff;
    border: 1px solid #ce3746;
    border-radius: 4px;
    padding: 4px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.btn-delete-row:hover {
    background-color: #c82333;
    color: rgb(255, 255, 255);
}

/* 修改表格头部样式以适应新增列 */
.splist-table th:last-child,
.splist-table td:last-child,
.register-table th:last-child,
.register-table td:last-child {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

/* 用户信息页面样式 */
.user-profile-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-profile-container h2 {
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #4c6fff;
}

.user-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 20px;
}

.info-group {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.info-group label {
    font-weight: bold;
    color: #555;
    width: 80px;
}

.info-group span {
    color: #333;
    flex: 1;
}

/* 用户菜单样式 */
.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
    min-width: 120px;
    margin-top: 5px;
}

.user-menu .menu-item {
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.user-menu .menu-item:hover {
    background-color: #f5f5f5;
}

/* 确保user-profile有相对定位 */
.user-profile {
    position: relative;
    cursor: pointer;
}

/* 文件上传相关样式 */
.file-item {
    display: flex;
    align-items: center;
    margin: 5px 0;
    padding: 5px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 12px;
}

.file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 5px;
}

.file-size {
    color: #666;
    margin-right: 5px;
}

.btn-delete-file {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0 5px;
    font-size: 16px;
}

.btn-delete-file:hover {
    color: #c82333;
}

input[type="file"] {
    max-width: 200px;
}

/* 交接查询搜索栏样式 */
.assShiftSearch,
.assAnalysis,
.asssearch {
        background-color: #f5f5f5;
        padding: 20px;
        border-radius: 4px;
        margin-bottom: 20px;
}

.assShiftSearch-row,
.assAnalysis-row,
.asssearch-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.assShiftSearch-row label,
.assAnalysis-row label,
.asssearch-row label {
    font-size: 14px;
    color: #333;
    margin-right: 0;
}

.assShiftSearch-row select,
.assAnalysis-row select,
.asssearch-row select {
    width: 120px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 5px;
    font-size: 14px;
    color: #333;
    background-color: #fff;
}

.assShiftSearch-row input[type="text"],
.assAnalysis-row input[type="text"],
.asssearch-row input[type="text"] {
    width: 200px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 10px;
    font-size: 14px;
}

.assShiftSearch-row input[type="date"],
.assAnalysis-row input[type="date"],
.asssearch-row input[type="date"] {
    width: 130px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 5px;
    font-size: 14px;
}

.button-group {
    display: flex;
    gap: 10px;
}

.assShiftSearch-btn,
.assShiftSearchReset-btn,
.kpireset-btn,
.kpisearch-btn,
.btn-edit,
.assAnalysis-btn,
.assAnalysisReset-btn,
.asssearch-btn,
.assDownload-btn,
.assreset-btn {
    height: 28px;
    padding: 0 15px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.assShiftSearch-btn,
.kpisearch-btn,
.btn-edit,
.assAnalysis-btn,
.assDownload-btn,
.asssearch-btn {
    background-color: #4c6fff;
    color: white;
}

.assShiftSearchReset-btn,
.kpireset-btn,
.assAnalysisReset-btn,
.assreset-btn {
    background-color: #6c757d;
    color: white;
}

.assShiftSearch-btn:hover,
.kpisearch-btn:hover,
.btn-edit:hover,
.assAnalysis-btn:hover,
.assDownload-btn,
.asssearch-btn:hover {
    background-color: #3a5ae8;
}

.kpireset-btn:hover,
.assAnalysisReset-btn:hover,
.assreset-btn:hover {
    background-color: #5a6268;
}

/* 响应式布局调整 */
@media screen and (max-width: 1200px) {
    .assShiftSearch-row,
    .asssearch-row {
        flex-wrap: wrap;
    }

    .button-group {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
        justify-content: flex-end;
    }
}

.upload-form,
.notice-form {
    max-width: 600px;
    margin: 20px auto;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-group textarea {
    resize: vertical;
}

.file-info {
    margin-top: 5px;
    font-size: 0.9em;
    color: #666;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-start;
    margin-top: 20px;
}

.btn-submit,
.btn-reset {
    padding: 8px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}


.btn-reset {
    background-color: #f0f0f0;
    color: #333;
}


.btn-reset:hover {
    background-color: #e4e4e4;
}

/* 查询样式 */
.JIGsearch,
.kpisearch,
.kpiload,
.sparepartssearch,
.sparepartshistory,
.teachingsearch {
    background-color: #f5f5f5;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.JIGsearch-row,
.kpisearch-row,
.kpiload-row,
.sparepartssearch-row,
.sparepartshistory-row,
.teachingsearch-row {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.JIGsearch-row label,
.kpisearch-row label,
.kpiload-row label,
.sparepartssearch-row label,
.sparepartshistory-row label,
.teachingsearch-row label {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
}

.JIGsearch-row select,
.kpisearch-row select,
.kpiload-row select,
.sparepartssearch-row select,
.sparepartshistory-row select,
.teachingsearch-row select {
    width: 120px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 5px;
    font-size: 14px;
    color: #333;
    background-color: #fff;
}

.JIGsearch-row input[type="text"],
.kpisearch-row input[type="text"],
.kpiload-row  input[type="text"],
.sparepartssearch-row input[type="text"] ,
.sparepartshistory-row input[type="text"],
.teachingsearch-row input[type="text"] {
    width: 200px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 10px;
    font-size: 14px;
}

.JIGsearch-row .button-group,
.kpisearch-row .button-group,
.kpiload-row  .button-group,
.sparepartssearch-row .button-group,
.sparepartshistory-row .button-group,
.teachingsearch-row .button-group {
    margin-left: auto;
    display: flex;
    gap: 10px;
}

.JIGsearch-btn,
.JIGreset-btn,
.sparepartssearch-btn,
.sparepartsreset-btn,
.sparepartsdownload-btn,
.sparepartshistorysearch-btn,
.sparepartshistoryreset-btn,
.sparepartshistorydownload-btn,
.teachingsearch-btn,
.teachingreset-btn {
    height: 28px;
    padding: 0 15px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.JIGsearch-btn,
.sparepartssearch-btn,
.sparepartshistorysearch-btn,
.sparepartsdownload-btn,
.sparepartshistorysearch-btn,
.sparepartshistoryreset-btn,
.sparepartshistorydownload-btn,
.teachingsearch-btn {
    background-color: #4c6fff;
    color: white;
}

.JIGreset-btn,
.sparepartsreset-btn,
.sparepartshistoryreset-btn,
.teachingreset-btn {
    background-color: #6c757d;
    color: white;
}

.JIGsearch-btn:hover,
.sparepartssearch-btn:hover,
.sparepartsdownload-btn:hover,
.sparepartshistorysearch-btn:hover,
.sparepartshistorydownload-btn:hover,
.teachingsearch-btn:hover {
    background-color: #3a5ae8;
}

.JIGreset-btn:hover,
.sparepartsreset-btn:hover,
.sparepartshistoryreset-btn:hover,
.teachingreset-btn:hover {
    background-color: #5a6268;
}

/* 资料列表样式 */
.teachinglist {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.teachinglist .data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.teachinglist .data-table th,
.teachinglist .data-table td {
    padding: 12px 8px;
    text-align: left;
    border: 1px solid #ddd;
}

.teachinglist .data-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    white-space: nowrap;
}

.teachinglist .data-table td {
    vertical-align: middle;
}

/* 设置各列宽度 */
.teachinglist .data-table th:nth-child(2) {/* 文件名 */
    width: 45%;
    text-align: center;
}
.teachinglist .data-table td:nth-child(2) {
    width: 45%;
}

.teachinglist .data-table th:nth-child(1), /* 标题 */
.teachinglist .data-table td:nth-child(1) {
    width: 20%;
    text-align: center;
}

.teachinglist .data-table th:nth-child(3), /* 分类 */
.teachinglist .data-table td:nth-child(3) {
    width: 5%;
    text-align: center;
}

.teachinglist .data-table th:nth-child(4), /* unit */
.teachinglist .data-table td:nth-child(4) {
    width: 8%;
    text-align: center;
}

.teachinglist .data-table th:nth-child(5), /* 科室 */
.teachinglist .data-table td:nth-child(5) {
    width: 5%;
    text-align: center;
}

.teachinglist .data-table th:nth-child(6), /* 上传人 */
.teachinglist .data-table td:nth-child(6) {
    width: 5%;
    text-align: center;
}

.teachinglist .data-table th:nth-child(7), /* 上传时间 */
.teachinglist .data-table td:nth-child(7) {
    width: 7%;
    text-align: center;
}   

.teachinglist .data-table th:nth-child(8), /* 操作 */
.teachinglist .data-table td:nth-child(8) {
    width: 5%;
    text-align: center;
}

/* 操作按钮样式 */
.teachinglist .btn-download {
    padding: 4px 8px;
    background-color: #4c6fff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.teachinglist .btn-download:hover {
    background-color: #3a5ae8;
}

/* 响应式布局调整 */
@media screen and (max-width: 1200px) {
    .teachingsearch-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .teachingsearch-row .button-group {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
        justify-content: flex-end;
    }

    .teachingsearch-row input[type="text"],
    .teachingsearch-row select {
        width: 100%;
    }
}

/* 添加跟进状态样式 */
.follow-status,
.closed-status {
    padding: 4px 8px;
    border-radius: 4px;  /* 这里定义圆角 */
    font-size: 14px;
    display: inline-block;
    min-width: 40px;
    text-align: center;
}

/* "是"的样式 */
.follow-yes,
.closed-yes {
    background-color: hwb(140 90% 0%);
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

/* "否"的样式 */
.follow-no,
.closed-no {
    background-color: hsl(0, 0%, 90%);
    color: hsl(0, 0%, 50%);
    border: 1px solid hsl(0, 0%, 80%);
}

/* 居中对齐的列 */
.splist-list .data-table td,
.spneedlist-list .data-table td,
.sphistory-list .data-table td,
.assregister .register-table td:nth-child(1),  /* LINE */
.assregister .register-table td:nth-child(2),  /* UNIT */
.assregister .register-table td:nth-child(3),  /* 故障分类 */
.assregister .register-table td:nth-child(7),  /* 故障部件 */
.assregister .register-table td:nth-child(8),  /* 故障代码 */
.assregister .register-table td:nth-child(9),  /* 需要跟进 */
.assregister .register-table td:nth-child(10),  /* 故障图片 */ 
.assregister .register-table td:nth-child(11),  /* 操作 */ 
.asslist .data-table td:nth-child(1),  /* 日期 */
.asslist .data-table td:nth-child(2),  /* 班次 */
.asslist .data-table td:nth-child(3),  /* LINE */
.asslist .data-table td:nth-child(4),  /*UNIT */
.asslist .data-table td:nth-child(8),  /* 故障分类 */
.asslist .data-table td:nth-child(9),  /* 故障部位 */
.asslist .data-table td:nth-child(10),   /* 故障代码 */ 
.asslist .data-table td:nth-child(11), /* 需要跟进 */
.asslist .data-table td:nth-child(12) /* 是否结案 */ {
    text-align: center;
}

/* 左对齐的列 */
.assregister .register-table td:nth-child(5),  /* 现象 */
.assregister .register-table td:nth-child(6),  /* 原因 */
.assregister .register-table td:nth-child(7),  /* 处理内容 */
.asslist .data-table td:nth-child(5),  /* 现象 */
.asslist .data-table td:nth-child(6),  /* 分析 */
.asslist .data-table td:nth-child(7)  /* 对策 */
{
    text-align: left;
}

/* 表头始终居中对齐 */
.assregister .register-table th,
.asslist .data-table th {
    background-color: #f5f5f5;
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    white-space: nowrap;
}

/* 交接列表表格列宽控制 */
.assregister .register-table ,
.asslist .data-table {
    /*table-layout: fixed;*/  /* 使用固定列宽布局 */
    width: 100%;
}

/* 设置各列宽度 */
.asslist .data-table th:nth-child(1), /* 日期 */
.asslist .data-table td:nth-child(1) {
    width: 5%;
}

.asslist .data-table th:nth-child(2), /* 班次 */
.asslist .data-table td:nth-child(2) {
    width: 4%;
}

.asslist .data-table th:nth-child(3), /* LINE */
.asslist .data-table td:nth-child(3) {
    width: 4%;
}

.asslist .data-table th:nth-child(4), /* UNIT */
.asslist .data-table td:nth-child(4) {
    width: 4%;
}

.asslist .data-table th:nth-child(5), /* 现象 */
.asslist .data-table td:nth-child(5) {
    width: 17%;
}

.asslist .data-table th:nth-child(6), /* 分析 */
.asslist .data-table td:nth-child(6) {
    width: 17%;
}

.asslist .data-table th:nth-child(7), /* 对策 */
.asslist .data-table td:nth-child(7) {
    width: 17%;
}

.asslist .data-table th:nth-child(8), /* 故障分类 */
.asslist .data-table td:nth-child(8) {
    width: 5%;
}

.asslist .data-table th:nth-child(9), /* 故障部位 */
.asslist .data-table td:nth-child(9) {
    width: 5%;
}

.asslist .data-table th:nth-child(10), /* 故障代码 */
.asslist .data-table td:nth-child(10) {
    width: 5%;
}

.asslist .data-table th:nth-child(11), /* 需要跟进 */
.asslist .data-table td:nth-child(11) {
    width: 5%;
}

.asslist .data-table th:nth-child(12), /* 是否结案 */
.asslist .data-table td:nth-child(12) {
    width: 5%;
}

.asslist .data-table th:nth-child(13), /* 操作 */
.asslist .data-table td:nth-child(13) {
    width: 7%;
}

/* 处理长文本溢出 */
.asslist .data-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;  /* 允许文本换行 */
    word-wrap: break-word;  /* 长单词换行 */
}

/* 现象、分析、对策列允许更多行显示 */
.asslist .data-table td:nth-child(5),
.asslist .data-table td:nth-child(6),
.asslist .data-table td:nth-child(4) {
    max-height: 100px;
    overflow-y: auto;
}

.main-content h3 {
    margin-left: 10px;  /* 添加左边距 */
}

.assregister .register-table  {
    /*table-layout: fixed;*/  /* 使用固定列宽布局 */
    width: 100%;
}

/* 设置各列宽度 */
.assregister .register-table th:nth-child(1), /* 班次 */
.assregister .register-table td:nth-child(1) {
    width: 5%;
}

.assregister .register-table th:nth-child(2), /* LINE */
.assregister .register-table td:nth-child(2) {
    width: 5%;
}

.assregister .register-table th:nth-child(3), /* UNIT */
.assregister .register-table td:nth-child(3) {
    width: 5%;
}

.assregister .register-table th:nth-child(4), /* 故障分类 */
.assregister .register-table td:nth-child(4) {
    width: 5%;
}

.assregister .register-table th:nth-child(5), /* 现象 */
.assregister .register-table td:nth-child(5) {
    width: 18%;
}

.assregister .register-table th:nth-child(6), /* 原因 */
.assregister .register-table td:nth-child(6) {
    width: 18%;
}

.assregister .register-table th:nth-child(7), /* 处理内容 */
.assregister .register-table td:nth-child(7) {
    width: 18%;
}

.assregister .register-table th:nth-child(8), /* 故障部件 */
.assregister .register-table td:nth-child(8) {
    width: 5%;
}

.assregister .register-table th:nth-child(9), /* 故障代码 */
.assregister .register-table td:nth-child(9) {
    width: 5%;
}

.assregister .register-table th:nth-child(10), /* 需要跟进 */
.assregister .register-table td:nth-child(10) {
    width: 5%;
}

.assregister .register-table th:nth-child(11), /* 故障图片 */
.assregister .register-table td:nth-child(11) {
    width: 5%;
}

.assregister .register-table th:nth-child(12), /* 操作 */
.assregister .register-table td:nth-child(12) {
    width: 5%;
}

/* 处理长文本溢出 */
.assregister .register-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;  /* 允许文本换行 */
    word-wrap: break-word;  /* 长单词换行 */
}

/* 现象、分析、对策列允许更多行显示 */
.assregister .register-table td:nth-child(6),
.assregister .register-table td:nth-child(4),
.assregister .register-table td:nth-child(5) {
    max-height: 100px;
    overflow-y: auto;
}

.associate-list .data-table td:nth-child(1),
.associate-list .data-table td:nth-child(2),
.associate-list .data-table td:nth-child(6),
.associate-list .data-table td:nth-child(7),
.associate-list .data-table td:nth-child(8){
    text-align:center;
}

.associate-list .data-table td:nth-child(3),
.associate-list .data-table td:nth-child(4),
.associate-list .data-table td:nth-child(5){
    text-align:left;
}

.associate-list-closed .data-table td:nth-child(1),
.associate-list-closed .data-table td:nth-child(2),
.associate-list-closed .data-table td:nth-child(6),
.associate-list-closed .data-table td:nth-child(7),
.associate-list-closed .data-table td:nth-child(8){
    text-align:center;
}

.associate-list-closed .data-table td:nth-child(3),
.associate-list-closed .data-table td:nth-child(4),
.associate-list-closed .data-table td:nth-child(5){
    text-align:left;
}

.site-content {
    display: inline-block;
    text-align: center;
    padding: 20px;
    margin: 10px;
    min-width: 200px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.site-content:hover {
    background-color: whitesmoke;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

a.site-content  {
    text-decoration: none;
    color: #333;
    font-size: 18px;
    transition: color 0.3s ease;
}

a.site-content a:hover {
    color: #4c6fff;
}

/* 设置各列宽度 */
.spneedlist-list .data-table th:nth-child(1), /* 备品名称 */
.spneedlist-list .data-table td:nth-child(1) {
    width: 5%;
}

.spneedlist-list .data-table th:nth-child(2), /* 型号 */
.spneedlist-list .data-table td:nth-child(2) {
    width: 10%;
}

.spneedlist-list .data-table th:nth-child(3), /* 库存位置 */
.spneedlist-list .data-table td:nth-child(3) {
    width: 5%;
}

.spneedlist-list .data-table th:nth-child(4), /* 料号 */
.spneedlist-list .data-table td:nth-child(4) {
    width: 5%;
}

.spneedlist-list .data-table th:nth-child(5), /* 使用位置 */
.spneedlist-list .data-table td:nth-child(5) {
    width: 8%;
}

.spneedlist-list .data-table th:nth-child(6), /* 数量 */
.spneedlist-list .data-table td:nth-child(6) {
    width: 3%;
}

.spneedlist-list .data-table th:nth-child(7), /* 原因 */
.spneedlist-list .data-table td:nth-child(7) {
    width: 10%;
}

.spneedlist-list .data-table th:nth-child(8), /* 科室 */
.spneedlist-list .data-table td:nth-child(8) {
    width: 5%;
}

.spneedlist-list .data-table th:nth-child(9), /* 申请人 */
.spneedlist-list .data-table td:nth-child(9) {
    width: 3%;
}

.spneedlist-list .data-table th:nth-child(10), /* 日期 */
.spneedlist-list .data-table td:nth-child(10) {
    width: 3%;
}

.spneedlist-list .data-table th:nth-child(11), /* 状态 */
.spneedlist-list .data-table td:nth-child(11) {
    width: 3%;
}

.spneedlist-list .data-table th:nth-child(12), /* 操作 */
.spneedlist-list .data-table td:nth-child(12) {
    width: 3%;
}

/* 设置各列宽度 */
.splist-list .data-table th:nth-child(1), /* 备品名称 */
.splist-list .data-table td:nth-child(1) {
    width: 10%;
}

.splist-list .data-table th:nth-child(2), /* 型号 */
.splist-list .data-table td:nth-child(2) {
    width: 10%;
}

.splist-list .data-table th:nth-child(3), /* 库存位置 */
.splist-list .data-table td:nth-child(3) {
    width: 5%;
}

.splist-list .data-table th:nth-child(4), /* 料号 */
.splist-list .data-table td:nth-child(4) {
    width: 8%;
}

.splist-list .data-table th:nth-child(5), /* 使用位置 */
.splist-list .data-table td:nth-child(5) {
    width: 8%;
}

.splist-list .data-table th:nth-child(6), /* 数量 */
.splist-list .data-table td:nth-child(6) {
    width: 5%;
}

.splist-list .data-table th:nth-child(7), /* 科室 */
.splist-list .data-table td:nth-child(7) {
    width: 5%;
}

.splist-list .data-table th:nth-child(8), /* 状态 */
.v .data-table td:nth-child(8) {
    width: 5%;
}

.splist-list .data-table th:nth-child(9), /* 操作 */
.splist-list .data-table td:nth-child(9) {
    width: 5%;
}


/* 处理长文本溢出 */
.splist-list .data-table td,
.spneedlist-list .data-table td,
.sphistory-list .data-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;  /* 允许文本换行 */
    word-wrap: break-word;  /* 长单词换行 */
}

/* 设置各列宽度 */
.sphistory-list .data-table th:nth-child(1), /* 名称 */
.sphistory-list .data-table td:nth-child(1) {
    width: 10%;
}

.sphistory-list .data-table th:nth-child(2), /* 型号 */
.sphistory-list .data-table td:nth-child(2) {
    width: 10%;
}

.sphistory-list .data-table th:nth-child(3), /* 动作 */
.sphistory-list .data-table td:nth-child(3) {
    width: 5%;
}

.sphistory-list .data-table th:nth-child(4), /* 数量 */
.sphistory-list .data-table td:nth-child(4) {
    width: 5%;
}

.sphistory-list .data-table th:nth-child(5), /* 库存 */
.sphistory-list .data-table td:nth-child(5) {
    width: 5%;
}

.sphistory-list .data-table th:nth-child(6), /* 使用位置 */
.sphistory-list .data-table td:nth-child(6) {
    width: 10%;
}

.sphistory-list .data-table th:nth-child(7), /* 操作人   */
.sphistory-list .data-table td:nth-child(7) {
    width: 5%;
}

.sphistory-list .data-table th:nth-child(8), /* 日期 */
.sphistory-list .data-table td:nth-child(8) {
    width: 5%;
}



.line-table {
    display: inline-block;
    vertical-align: top;
    margin-right: 10px;
    margin-bottom: 20px;    
    font-size: 14px;
}



.tt-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;

}

.tt-table th {
    background-color: #f4fcff;
    padding: 4px 10px;
    text-align: center;
    border: 1px solid #ddd;
    white-space: nowrap;
}

.tt-table td {
    padding: 4px 10px;
    border: 1px solid #ddd;
    vertical-align: middle;
    text-align: center;
}


/* 班组交接登录表格样式 */

.shift-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}


.shift-table th,
.shift-table td {
    padding: 10px;
    border: 1px solid #ddd;
}


.shift-table th {
    background-color: #f5f5f5;
    text-align: right;
    width: 100px;
    white-space: nowrap;
}


.shift-table td {
    background-color: #fff;
}


.shift-table select,
.shift-table input[type="text"],
.shift-table textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}


.shift-table textarea {
    resize: vertical;
    min-height: 60px;
}


/* 修改shift-table的样式 */

.shift-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}


.shift-table th,
.shift-table td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: center;
    vertical-align: middle;
}


.shift-table th {
    background-color: #f5f5f5;
    white-space: nowrap;
    font-weight: bold;
}


.shift-table select,
.shift-table input[type="text"],
.shift-table textarea {
    width: 100%;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}


.shift-table textarea {
    resize: vertical;
    min-height: 40px;
}

/* 添加行按钮样式 */
.btn-add-shift-rows {
    background-color: #4c6fff;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.btn-add-shift-rows:hover {
    background-color: #3a5ae8;
}

/* 单选按钮组样式 */
.radio-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    padding: 8px;
    height: 100%;
    box-sizing: border-box;
}

.radio-group label {
    display: inline-flex;  /* 改为inline-flex */
    align-items: center;
    gap: 5px;
    cursor: pointer;
    margin: 0 5px;  /* 添加水平间距 */
}

.radio-group input[type="radio"] {
    margin: 0;
    cursor: pointer;
    width: auto;  /* 防止继承100%宽度 */
}

.radio-group span {
    font-size: 14px;
    white-space: nowrap;  /* 防止文字换行 */
}

/* 班组交接人力样式 */
.shift-manpower {
        background-color: #f5f5f5;
        padding: 20px;
        border-radius: 4px;
        margin-bottom: 20px;
}

.shift-manpower-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.shift-manpower-row label {
    font-size: 14px;
    color: #333;
    margin-right: 0;
}

.shift-manpower-row select {
    width: 120px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 5px;
    font-size: 14px;
    color: #333;
    background-color: #fff;
}

.shift-manpower-row input[type="text"] {
    width: 200px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 10px;
    font-size: 14px;
}

.shift-manpower-row input[type="date"] {
    width: 130px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 5px;
    font-size: 14px;
}

.shift-button-group {
    display: flex;
    gap: 10px;
}

.assShift-submit-btn,
.assShift-reset-btn {
    height: 28px;
    padding: 0 15px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.assShift-submit-btn {
    background-color: #4c6fff;
    color: white;
}

.assShift-reset-btn {
    background-color: #6c757d;
    color: white;
}

.assShift-submit-btn:hover {
    background-color: #3a5ae8;
}

.assShift-reset-btn:hover {
    background-color: #5a6268;
}

/* 响应式布局调整 */
@media screen and (max-width: 1200px) {
    .shift-manpower-row {
        flex-wrap: wrap;
    }

    .shift-button-group {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
        justify-content: flex-end;
    }
}

/* 确保单选按钮单元格与其他单元格对齐 */
.shift-table td.radio-group {
    vertical-align: middle;
    padding: 8px;
    height: 100%;
    min-height: 40px;  /* 与其他单元格保持一致的最小高度 */
}

/* 修改删除按钮样式 */
.action-column {
    width: 60px;
    padding: 8px !important;
}

.btn-delete-shift-row {
    background-color: #ce3746;
    color: #ffffff;
    border: 1px solid #ce3746;
    border-radius: 4px;
    padding: 4px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.btn-delete-shift-row:hover {
    background-color: #c82333;
    color: rgb(255, 255, 255);
}

/* 修改表格头部样式以适应新增列 */
.shift-table th:last-child,
.shift-table td:last-child {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}


/* 居中对齐的列 */
.assShift .shift-table td:nth-child(1),  /* LINE */
.assShift .shift-table td:nth-child(2),  /* UNIT */
.assShift .shift-table td:nth-child(3),  /* 故障分类 */
.assShift .shift-table td:nth-child(7),  /* 故障部件 */
.assShift .shift-table td:nth-child(8),  /* 故障代码 */
.assShift .shift-table td:nth-child(9)  /* 需要跟进 */
{
    text-align: center;
}

/* 左对齐的列 */
.assShift .shift-table td:nth-child(5),  /* 现象 */
.assShift .shift-table td:nth-child(6),  /* 原因 */
.assShift .shift-table td:nth-child(4)  /* 处理内容 */
{
    text-align: left;
}

/* 表头始终居中对齐 */
.assShift .shift-table th {
    background-color: #f5f5f5;
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    white-space: nowrap;
}

/* 交接列表表格列宽控制 */
.assShift .shift-table{
    /*table-layout: fixed;*/  /* 使用固定列宽布局 */
    width: 100%;
}

/* 设置各列宽度 */
.assShift .shift-table th:nth-child(1), /* LINE */
.assShift .shift-table td:nth-child(1), 
.assShift .shift-table th:nth-child(2), /* UNIT */
.assShift .shift-table td:nth-child(2),
.assShift .shift-table th:nth-child(3), /* 故障分类 */
.assShift .shift-table td:nth-child(3),
.assShift .shift-table th:nth-child(7), /* 需要跟进 */
.assShift .shift-table td:nth-child(7),
.assShift .shift-table th:nth-child(8), /* 故障图片 */
.assShift .shift-table td:nth-child(8),
.assShift .shift-table th:nth-child(9), /* 操作 */
.assShift .shift-table td:nth-child(9) 
{
    width: 6%;
}

.assShift .shift-table th:nth-child(5), /* 现象 */
.assShift .shift-table td:nth-child(5),
.assShift .shift-table th:nth-child(6), /* 原因 */
.assShift .shift-table td:nth-child(6),
.assShift .shift-table th:nth-child(4), /* 处理内容 */
.assShift .shift-table td:nth-child(4)  
{
    width: 17%;
}

/* 处理长文本溢出 */
.assShift .shift-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;  /* 允许文本换行 */
    word-wrap: break-word;  /* 长单词换行 */
}

/* 现象、分析、对策列允许更多行显示 */
.assShift .shift-table td:nth-child(5),
.assShift .shift-table td:nth-child(6),
.assShift .shift-table td:nth-child(4) {
    max-height: 100px;
    overflow-y: auto;
}

/* 表单操作按钮样式 */
.shift-form-actions {
    display: flex;
    justify-content: flex-start;  /*头部位置*/  /*flex-end; 尾部*/
    gap: 10px;
    margin-top: 20px;
}

.shift-form-actions {
    text-align: center;
    margin-top: 20px;
}

.shift-form-actions button {
    padding: 10px 20px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-shift-submit,
.btn-shift-cancel {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.btn-shift-submit {
    background-color: #4CAF50;
    color: white;
}

.btn-shift-cancel {
    background-color: #6c757d;
    color: white;
}

.btn-shift-reset {
    background-color: #6c757d;
    color: white;
}

.btn-shift-submit:hover {
    background-color: #3a5ae8;
}

.btn-shift-cancel:hover {
    background-color: #5a6268;
}

.btn-shift-reset:hover {
    background-color: #5a6268;
}

/* shift交接查询 */
.data-table1,
.data-table2
{
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table1 th, 
.data-table2 th 
{
    background-color: #f5f5f5;
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    white-space: nowrap;
}

.data-table1 td,
.data-table2 td 
{
    padding: 8px;
    border: 1px solid #ddd;
    vertical-align: middle;
}

/* 操作按钮列度控制 */
.data-table2 th:last-child,
.data-table2 td:last-child {
    width: 50px;
    text-align: center;
}

/* 居中对齐的列 */
.assShiftManpower .data-table1 td, 
.assShiftManpower .data-table2 td:nth-child(1),  /* 班次 */
.assShiftManpower .data-table2 td:nth-child(2),  /* 分类 */
.assShiftManpower .data-table2 td:nth-child(3),  /* LINE */
.assShiftManpower .data-table2 td:nth-child(4),  /* UNIT */
.assShiftManpower .data-table2 td:nth-child(8),  /* 需要跟进 */
.assShiftManpower .data-table2 td:nth-child(9),  /* 是否结案 */
.assShiftManpower .data-table2 td:nth-child(10),  /* 谁跟进 */ 
.assShiftManpower .data-table2 td:nth-child(11)  /* 操作 */ 
{
    text-align: center;
}

/* 左对齐的列 */
.assShiftManpower .data-table2 td:nth-child(5),  /* 现象 */
.assShiftManpower .data-table2 td:nth-child(6),  /* 分析 */
.assShiftManpower .data-table2 td:nth-child(7)  /* 对策 */
{
    text-align: left;
}

/* 表头始终居中对齐 */
.assShiftManpower .data-table1 th,
.assShiftManpower .data-table2 th {
    background-color: #f5f5f5;
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    white-space: nowrap;
}

/* 交接列表表格列宽控制 */
.assShiftManpower .data-table1, 
.assShiftManpower .data-table2 
{
    /*table-layout: fixed;*/  /* 使用固定列宽布局 */
    width: 100%;
}

/* 设置各列宽度 */
.assShiftManpower .data-table1 th:nth-child(1), /* 日期 */
.assShiftManpower .data-table1 td:nth-child(1),
.assShiftManpower .data-table1 th:nth-child(2), /*  管辖区域 */
.assShiftManpower .data-table1 td:nth-child(2),
.assShiftManpower .data-table1 th:nth-child(3), /* 班组 */
.assShiftManpower .data-table1 td:nth-child(3),
.assShiftManpower .data-table1 th:nth-child(4), /* 班次 */
.assShiftManpower .data-table1 td:nth-child(4),
.assShiftManpower .data-table1 th:nth-child(5), /* 本班应到人力 */
.assShiftManpower .data-table1 td:nth-child(5),
.assShiftManpower .data-table1 th:nth-child(6), /* 本班实到人力 */
.assShiftManpower .data-table1 td:nth-child(6),
.assShiftManpower .data-table1 th:nth-child(7), /* 加班人力 */
.assShiftManpower .data-table1 td:nth-child(7)
{
    width: 14%;
}

.assShiftManpower .data-table2 th:nth-child(1), /* 班次 */
.assShiftManpower .data-table2 td:nth-child(1) {
    width: 5%;
}

.assShiftManpower .data-table2 th:nth-child(2), /* 分类 */
.assShiftManpower .data-table2 td:nth-child(2) {
    width: 5%;
}

.assShiftManpower .data-table2 th:nth-child(3), /* LINE */
.assShiftManpower .data-table2 td:nth-child(3) {
    width: 5%;
}

.assShiftManpower .data-table2 th:nth-child(4), /* UNIT */
.assShiftManpower .data-table2 td:nth-child(4) {
    width: 5%;
}

.assShiftManpower .data-table2 th:nth-child(5), /* 问题点 */
.assShiftManpower .data-table2 td:nth-child(5) {
    width: 20%;
}

.assShiftManpower .data-table2 th:nth-child(6), /* 原因 */
.assShiftManpower .data-table2 td:nth-child(6) {
    width: 20%;
}

.assShiftManpower .data-table2 th:nth-child(7), /* 处理内容 */
.assShiftManpower .data-table2 td:nth-child(7) {
    width: 20%;
}

.assShiftManpower .data-table2 th:nth-child(8), /* 需要跟进 */
.assShiftManpower .data-table2 td:nth-child(8) {
    width: 5%;
}

.assShiftManpower .data-table2 th:nth-child(9), /* 是否结案 */
.assShiftManpower .data-table2 td:nth-child(9) {
    width: 5%;
}

.assShiftManpower .data-table2 th:nth-child(10), /* 谁跟进 */
.assShiftManpower .data-table2 td:nth-child(10) {
    width: 5%;
}

.assShiftManpower .data-table2 th:nth-child(11), /* 操作 */
.assShiftManpower .data-table2 td:nth-child(11) {
    width: 5%;
}

/* 处理长文本溢出 */
.assShiftManpower .data-table2 td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;  /* 允许文本换行 */
    word-wrap: break-word;  /* 长单词换行 */
}

/* 现象、分析、对策列允许更多行显示 */
.assShiftManpower .data-table2 td:nth-child(5),
.assShiftManpower .data-table2 td:nth-child(6),
.assShiftManpower .data-table2 td:nth-child(7) {
    max-height: 100px;
    overflow-y: auto;
}

/* 故障登录界面左右布局 */
.form-container {
    max-width: 1500px;
  display: flex;
  flex-wrap: wrap;
  margin: 20px auto;
    padding: 20px;
  background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.left-column,
.right-column {
  padding: 0 10px;
  box-sizing: border-box;
}

.left-column {
  flex: 0 0 40%;
}

.right-column {
  flex: 0 0 60%;
}

.left-column {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.left-column .form-group {
  width: calc(50% - 16px);
  margin: 0 8px 15px 8px;
  display: inline-block;
  vertical-align: top;
}

.left-column .form-group:last-child {
  width: calc(100% - 16px);
}

.right-column .form-group {
  margin-bottom: 15px;
  width: 100%;
  display: block;
  clear: both;
}

.right-column textarea {
  width: 100%;
  resize: vertical;
  min-height: 100px;
}

.edit-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: inherit;
  font-size: inherit;
  resize: vertical;
  box-sizing: border-box;
  margin: 0 auto;
}

.edit-textarea:focus {
  border-color: #0088cc;
  outline: none;
  box-shadow: 0 0 5px rgba(0,136,204,0.5);
}

.change-status {
  margin: 15px 0;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.change-status h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

.change-status label {
  margin-right: 20px;
  cursor: pointer;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .left-column,
  .right-column {
    flex: 0 0 100%;
  }
  
  .left-column {
    margin-bottom: 20px;
  }
}

/* 设置各列宽度 */
.fault-list .data-table th:nth-child(1), /* 日期 */
.fault-list .data-table td:nth-child(1) {
    width: 3%;
    text-align: center;
}
.fault-list .data-table th:nth-child(2), /* 处理用时 */
.fault-list .data-table td:nth-child(2) {
    width: 4%;
    text-align: center;
}
.fault-list .data-table th:nth-child(3), /* 问题点 */
.fault-list .data-table td:nth-child(3) {
    width: 10%;
    text-align: center;
}
.fault-list .data-table th:nth-child(4), /* 现象 */
.fault-list .data-table td:nth-child(4) {
    width: 24%;
    text-align: center;
}
.fault-list .data-table th:nth-child(5), /* 改善 */
.fault-list .data-table td:nth-child(5) {
    width: 25%;
    text-align: center;
}
.fault-list .data-table th:nth-child(6), /* 科室 */
.fault-list .data-table td:nth-child(6) {
    width: 6%;
    text-align: center;
}
.fault-list .data-table th:nth-child(7), /* line */
.fault-list .data-table td:nth-child(7) {
    width: 3%;
    text-align: center;
}
.fault-list .data-table th:nth-child(8), /* unit */
.fault-list .data-table td:nth-child(8) {
    width: 3%;
    text-align: center;
}
.fault-list .data-table th:nth-child(9), /* 分类 */
.fault-list .data-table td:nth-child(9) {
    width: 3%;
    text-align: center;
}
.fault-list .data-table th:nth-child(10), /* 方向 */
.fault-list .data-table td:nth-child(10) {
    width: 3%;
    text-align: center;
}

.fault-list .data-table th:nth-child(11), /* 问题状态 */
.fault-list .data-table td:nth-child(11) {
    width: 3%;
    text-align: center;
}
.fault-list .data-table th:nth-child(12), /* 担当 */
.fault-list .data-table td:nth-child(12) {
    width: 4%;
    text-align: center;
}
.fault-list .data-table th:nth-child(13), /* 操作 */
.fault-list .data-table td:nth-child(13) {
    width: 4%;
    text-align: center;
}

/* Layui布局基础样式 */
.layui-layout-body {
    overflow: hidden;
}

.layui-layout-admin {
    height: 100vh;
}

/* Layui样式覆盖 - 将绿色改为蓝色 */
.layui-nav-tree {
    width: 200px;
    padding: 0;
    height: 100%;
}

.layui-nav-tree .layui-nav-item {
    display: block;
    width: 100%;
    line-height: 45px;
}

.layui-nav-tree .layui-nav-item a {
    height: 45px;
    line-height: 45px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #333;
}

.layui-nav-tree .layui-nav-item .layui-nav-tt {
    font-weight: bold;
    font-size: 17px;
}

.layui-nav-tree .layui-nav-item a:hover {
    background-color: #e6f3ff;
    color: #4c6fff;
}

/* 将layui的绿色主题改为蓝色 */
.layui-nav .layui-this:after,
.layui-nav-bar,
.layui-nav-tree .layui-nav-itemed:after {
    background-color: #4c6fff !important;
}

.layui-nav .layui-nav-child dd.layui-this a,
.layui-nav-child dd.layui-this {
    background-color: #4c6fff !important;
    color: #fff !important;
}

.layui-nav-tree .layui-nav-bar {
    width: 5px;
    height: 0;
    background-color: #4c6fff !important;
}

.layui-nav-itemed .layui-nav-child {
    display: block;
    padding: 0;
    background-color: #f8f9fa !important;
}

.layui-nav .layui-nav-child a:hover {
    background-color: #e6f3ff !important;
    color: #4c6fff !important;
}

/* 侧边栏背景色调整 */
.layui-side-scroll {
    background: #f8f9fa !important;
}

/* Tab标签样式调整 */
.layui-tab-title .layui-this {
    color: #4c6fff !important;
}

.layui-tab-title .layui-this:after {
    border-bottom-color: #4c6fff !important;
}

.layui-tab-brief > .layui-tab-title .layui-this {
    color: #4c6fff !important;
}

.layui-tab-brief > .layui-tab-title .layui-this:after {
    border-color: #4c6fff !important;
}

/* 右键菜单样式 */
.rightmenu {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 9999;
}

.rightmenu li {
    padding: 8px 16px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.rightmenu li:last-child {
    border-bottom: none;
}

.rightmenu li:hover {
    background-color: #e6f3ff;
    color: #4c6fff;
}

/* 菜单图标样式 */
.menu-icon {
    width: 20px;
    margin-right: 10px;
    text-align: center;
    color: #666;
}

/* 导航项激活状态 */
.layui-nav-tree .layui-nav-item.layui-nav-itemed > a {
    background-color: #f0f7ff !important;
    color: #4c6fff !important;
}

/* 子菜单项样式 */
.layui-nav .layui-nav-child dd a {
    font-size: 14px;
}

.layui-nav .layui-nav-child dd a:hover {
    background-color: #e6f3ff !important;
    color: #4c6fff !important;
}

/* 确保header样式与现有风格一致 */
.layui-layout-admin .layui-header {
    background: #f8f9fa !important;
    border-bottom: 1px solid #eee;
    height: 60px;
    /* line-height: 60px; */
}

/* Header内容样式 */
.header-left {
    float: left;
    display: flex;
    align-items: center;
    height: 60px;
}

.header-right {
    float: right;
    display: flex;
    align-items: center;
    height: 60px;
}

.header-title {
    margin-left: 20px;
    font-size: 18px;
    font-weight: bold;
    color: #333;
    cursor: pointer;
}

.user-profile {
    position: relative;
    display: flex;
    align-items: center;
    margin-left: 20px;
    cursor: pointer;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #4c6fff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 14px;
}

.header-icon {
    margin-right: 15px;
    font-size: 18px;
    color: #666;
}

/* 用户菜单样式 */
.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
    min-width: 120px;
    margin-top: 5px;
}

.user-menu .menu-item {
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.user-menu .menu-item:hover {
    background-color: #f5f5f5;
}

/* 用户信息弹窗样式 */
.user-info-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-info-modal .modal-content {
    background: white;
    border-radius: 8px;
    width: 400px;
    max-width: 90%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.user-info-modal .modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-info-modal .modal-header h2 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.user-info-modal .close {
    font-size: 24px;
    cursor: pointer;
    color: #999;
    line-height: 1;
}

.user-info-modal .close:hover {
    color: #333;
}

.user-info-modal .modal-body {
    padding: 20px;
}

.user-info-modal .user-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.user-info-modal .info-group {
    display: flex;
    align-items: center;
}

.user-info-modal .info-group label {
    width: 80px;
    font-weight: bold;
    color: #666;
}

.user-info-modal .info-group span {
    color: #333;
    flex: 1;
}

/* 主体内容区域调整 */
.layui-layout-admin .layui-body {
    top: 60px;
    bottom: 0;
    left: 200px;
    position: absolute;
    /* overflow: auto; */
}

/* 修复layui-body显示问题 */
.layui-body {
    position: absolute;
    left: 200px;
    top: 60px;
    right: 0;
    bottom: 0;
    overflow: auto;
    background: #fff;
}

/* 确保侧边栏固定 */
.layui-side {
    position: fixed;
    left: 0;
    top: 60px;
    width: 200px;
    height: calc(100vh - 60px);
    z-index: 999;
}

/* 确保header固定 */
.layui-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

/* Tab内容区域样式 */
.layui-tab-content {
    padding: 0;
    height: calc(100vh - 100px);
    overflow: auto;
}

.layui-tab-content iframe {
    border: none;
    width: 100%;
    height: calc(100vh - 140px);
    min-height: 600px;
}

/* Tab标题样式 */
.layui-tab-title {
    margin: 0;
    background: #f8f9fa;
    border-bottom: 1px solid #e6e6e6;
}

/* Tab项样式 */
.layui-tab-item {
    height: 100%;
    overflow: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        padding: 10px;
    }

    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.active {
        transform: translateX(0);
    }
}
