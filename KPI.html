<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"> 
    <meta http-equiv="Pragma" content="no-cache"> <meta http-equiv="Expires" content="0"> 
    <title>业绩管理 - 设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/tabs.js" defer></script>
    <script src="js/xlsx.js" defer></script>
    <script src="js/echarts.js" defer></script>
    <script src="js/KPI.js" defer></script>
</head>
<body style="margin: 0; padding: 10px;">

    <div class="main-content" style="margin-left: 0; margin-top: 0;">
        <div class="tabs"> 
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('kpisearch')">业绩查询</button>
                <button class="tab-button" onclick="switchTab('kpiload')">业绩管理</button>
            </div>
        </div>
            
        <!-- 业绩查询选项卡 -->
        <div id="kpisearchTab" class="tab-content active">
            <div class="kpisearch">
                <form id="kpisearchForm" action="#" method="get">
                    <div class="kpisearch-row">
                        <div id="projectSelect">
                            <label for="kpisearch-leader">领班</label> <select id="kpisearch-leader" name="leader" style="width: 90px;"></select>
                            <label for="kpisearch-key">关键词</label> <input type="text" id="kpisearch-key" name="key" placeholder="请输入关键词查询">
                            <label for="kpisearch-date">日期</label> <input type="date" id="kpisearch-date" name="date">
                        </div>

                        <div class="button-group">
                            <button type="reset" class="kpireset-btn">重置</button>
                            <button type="submit" class="kpisearch-btn">查询</button>
                        </div>
                    </div>
                </form>
            </div>
            <!-- KPI数据显示 -->
        </div>

        <!-- 业绩管理选项卡 -->
        <div id="kpiloadTab" class="tab-content">
            <div class="kpiload">
                <form id="kpiloadForm" action="#" method="get">
                    <div class="kpisearch-row">
                        <div class="button-group">
                            <button type="button" class="kpisearch-btn">编辑</button>
                            <button type="button" class="kpisearch-btn">提交</button>
                            <button type="button" class="kpireset-btn">取消</button>
                            <button type="button" class="kpireset-btn">导出</button>
                            <button id="updatebtn" type="button" class="kpireset-btn" style="display: none;">更新</button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- KPI显示表 -->
            <div>
                <table id="kpiTable" class="display" style="width:100%">
                    <thead>
                        <tr>
                            <th>日期</th><!--date-->
                            <th>班组</th><!--class-->
                            <th>班次</th><!--shift-->
                            <th>楼层</th><!--floor-->
                            <th>领班</th><!--leader-->
                            <th>业绩</th><!--kpi-->
                            <th>达成率</th><!--FP-->
                            <th>QSM</th><!--QSM-->
                            <th>品质异常</th><!--quality-->
                            <th>5S3D</th><!--5S3D-->
                            <th>环境安全</th><!--env-->
                            <th>信息安全</th><!--info-->
                            <th>操作</th>
                        </tr>
                    </thead>
                </table>
            </div>

            <!-- 在表格后添加分页控件 -->
            <div class="pagination">
                <div class="pagination-info">
                    共 <span class="total-count">0</span> 条记录，
                    每页 <select class="page-size">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select> 条
                </div>
                <div class="pagination-controls">
                    <button class="btn-first-page">首页</button>
                    <button class="btn-prev-page">上一页</button>
                    <span class="page-info">
                        第 <input type="number" class="current-page" min="1"> 页，
                        共 <span class="total-pages">0</span> 页
                    </span>
                    <button class="btn-next-page">下一页</button>
                    <button class="btn-last-page">末页</button>
                </div>
            </div>
        </div>

           

    </div>
</body>
</html> 