<?php
session_start();
header('Content-Type: application/json');

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);

// 添加调试日志
error_log('Received data: ' . print_r($data, true));

$message = $data['message'] ?? '';
$response = $data['response'] ?? '';
$username = $_SESSION['username'] ?? 'anonymous';

// 创建日志内容
$logEntry = date('Y-m-d H:i:s') . " | " . 
            "User: " . $username . " | " . 
            "Message: " . $message . " | " . 
            "Response: " . $response . "\n";

// 指定日志文件路径，使用当前日期作为文件名
$today = date('Y-m-d');
$logFile = __DIR__ . '/../logs/' . $today . '_chat_logs.txt';

// 确保日志目录存在
$logDir = __DIR__ . '/../logs';
if (!file_exists($logDir)) {
    mkdir($logDir, 0777, true);
}

// 写入日志
if (file_put_contents($logFile, $logEntry, FILE_APPEND)) {
    error_log('Successfully wrote to log file: ' . $logFile);
    echo json_encode(['success' => true]);
} else {
    error_log('Failed to write to log file: ' . $logFile);
    echo json_encode(['success' => false, 'message' => 'Failed to write log']);
}
?> 